# LLM Veri Ön İşleme Uygulaması Konfigürasyonu
# Von.md dosyasındaki 65 problemi çözmek için çoklu LLM provider desteği

# =============================================================================
# LLM PROVIDER SEÇİMİ
# =============================================================================
# Desteklenen provider'lar: "OLLAMA" veya "OPENROUTER"
LLM_PROVIDER = "OLLAMA"  # Varsayılan: Ollama

# =============================================================================
# OLLAMA AYARLARI
# =============================================================================
# Ollama sunucu ayarları (Load balancing için 3 URL)
# Hangi URL'lerin aktif olacağını seçebilirsiniz
OLLAMA_URL_1 = "http://ubuntu.ai-institute.uk:11434"
OLLAMA_URL_2 = "http://lgpu1.ai-institute.uk:11434"
OLLAMA_URL_3 = "http://lgpu2.ai-institute.uk:11434"

# Aktif Ollama URL'leri (True/False ile kontrol edin)
OLLAMA_URL_1_ACTIVE = True
OLLAMA_URL_2_ACTIVE = True
OLLAMA_URL_3_ACTIVE = True

OLLAMA_MODEL = "qwen3:8b"

# =============================================================================
# OPENROUTER AYARLARI
# =============================================================================
# OpenRouter API ayarları
OPENROUTER_API_KEY = "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"  # OpenRouter API anahtarınızı buraya girin
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
OPENROUTER_MODEL = "qwen/qwen3-8b"  # Varsayılan model

# Alternatif OpenRouter modelleri:
# - "anthropic/claude-3.5-sonnet" (Yüksek kalite)
# - "openai/gpt-4o" (OpenAI GPT-4)
# - "google/gemini-pro-1.5" (Google Gemini)
# - "meta-llama/llama-3.1-8b-instruct" (Llama 8B)
# - "mistralai/mistral-7b-instruct" (Mistral 7B)

# =============================================================================
# İŞLEME AYARLARI
# =============================================================================
MAX_WORKERS = 14  # Paralel işçi sayısı
BATCH_SIZE = 100  # Batch büyüklüğü
USE_CACHE = True  # LLM cache kullanımı
TIMEOUT = 30  # İstek zaman aşımı (saniye)

# =============================================================================
# LOG AYARLARI
# =============================================================================
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# =============================================================================
# MESAJ AYARLARI
# =============================================================================
# Sistem mesajları (Türkçe)
MESSAGES = {
    "processing": "İşleniyor",
    "completed": "Tamamlandı",
    "error": "Hata",
    "detected": "Tespit edildi",
    "fixed": "Düzeltildi",
    "worker_started": "İşçi başladı",
    "worker_completed": "İşçi tamamlandı"
}
