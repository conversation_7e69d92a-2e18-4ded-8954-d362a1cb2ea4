# LLM Veri Ön İşleme Uygulaması Konfigürasyonu
# Von.md dosyasındaki 65 problemi çözmek için Qwen3:8B modeli

# Ollama sunucu ayarları
OLLAMA_URL_1 = "http://ubuntu.ai-institute.uk:11434"
OLLAMA_URL_2 = "http://lgpu1.ai-institute.uk:11434"
OLLAMA_URL_3 = "http://lgpu2.ai-institute.uk:11434"
OLLAMA_MODEL = "qwen3:8b"

# İşleme ayarları
MAX_WORKERS = 14  # Paralel işçi sayısı
BATCH_SIZE = 100  # Batch büyüklüğü
USE_CACHE = True  # LLM cache kullanımı
TIMEOUT = 30  # İstek zaman aşımı (saniye)

# Log ayarları
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
